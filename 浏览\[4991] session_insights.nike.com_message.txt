POST /v1/accounts/1015810/events h2
host: insights.nike.com
content-length: 2409
xweb_xhr: 1
x-insert-key: D6zkf-3hVEO4bIR7I9o6sivPcju2hLNT
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
content-type: application/json
accept: */*
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1, i

[{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":*************,"eventType":"wechat_client_reported_image_load","route":"https://mp-static-assets.gc.nike.com/image/shop/share.svg","loadTime":17,"traceName":"default"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019341685,"eventType":"wechat_client_reported_image_load","route":"https://mp-static-assets.gc.nike.com/image/shop/share.svg","loadTime":17,"traceName":"default"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019348480,"req_uri":"https://wechat.nike.com.cn/onemp/menu_categories/v1","req_host":"https://wechat.nike.com.cn","req_path":"/onemp/menu_categories/v1","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","res_status":200,"trackId":"16c45385-61ac-4bc4-8d31-9fe5cdf9bcc9","networkType":"wifi","roundTripTime":0.526,"eventType":"wechatmp_client_api_performance"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019348784,"req_uri":"https://wechat.nike.com.cn/onemp/sub_menu_categories/v1","req_host":"https://wechat.nike.com.cn","req_path":"/onemp/sub_menu_categories/v1","req_method":"POST","req_header_content-type":"application/json; charset=UTF-8","res_status":201,"trackId":"7ee7d8b3-a156-4b52-a10b-b4ce1c09f11f","networkType":"wifi","roundTripTime":0.3,"eventType":"wechatmp_client_api_performance"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019349337,"req_uri":"https://wechat.nike.com.cn/onemp/category/tree/v1?productTypeId=6949&pageNum=1&pageSize=99","req_host":"https://wechat.nike.com.cn","req_path":"/onemp/category/tree/v1","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","res_status":200,"trackId":"805e405d-b2f2-4fc8-ae6c-90cca9d27564","networkType":"wifi","roundTripTime":0.55,"eventType":"wechatmp_client_api_performance"}]

h2 200
content-length: 63
content-type: text/json; charset=utf-8
nr-rate-limited: allowed
access-control-allow-methods: GET, POST, PUT, HEAD, OPTIONS
access-control-allow-credentials: true
access-control-allow-origin: *
x-served-by: cache-tyo11928-TYO
date: Fri, 01 Aug 2025 03:35:50 GMT

{"success":true, "uuid":"404a2140-0001-b643-b420-019863b345f0"}