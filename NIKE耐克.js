/**
 * Nike耐克小程序签到和任务脚本
 * 作者：Tianxx
 * 版本：2.0
 * 日期：2025-08-01
 * 功能：每日签到、浏览任务、分享任务、积分查询
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wx096c43d1829a7788'; // Nike小程序appid

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class NikeScript {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;

        // Nike相关配置
        this.nikeConfig = {
            wechatApiUrl: 'https://wechat.nike.com.cn',
            nikeApiUrl: 'https://api.nike.com.cn',
            accountsUrl: 'https://accounts.nike.com.cn',
            appId: 'wechat:mp:wx096c43d1829a7788',
            clientId: '5e02c316811ebcb9e6960bc4bdefdaf1'
        };

        // Nike认证信息
        this.nikeAuth = {
            accessToken: null,
            userId: null,
            expiresIn: null
        };
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    // 加载Nike认证信息
                    if (userCache.nikeAuth) {
                        this.nikeAuth = userCache.nikeAuth;
                    }

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] 微信Code: ${this.wxCode}`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                cacheExpireTime: expireTime,
                updateTime: Date.now(),
                nikeAuth: this.nikeAuth
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.wxCode) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试获取一个简单的信息来验证登录状态
            const testResult = await wxcode.getOpenid(this.wxid, this.appid);
            if (testResult.success) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 执行完整的数据获取流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的数据获取流程...`);

        // 1. 获取授权码并登录
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. 根据需要获取其他数据（这里可以根据业务需求调整）
        // await this.getUserOpenid();     // 获取openid，取消此行注释
        // await this.getMobileInfo();     // 获取手机号，取消此行注释
        // await this.getUserProfile();    // 获取云函数用户个人信息，取消此行注释

        // 3. 保存到缓存
        this.saveTokenCache();

        return true;
    }

    // Nike登录认证 - 使用HTTP请求
    async nikeLogin() {
        if (!this.wxCode) {
            console.log(`❌ 缺少微信授权码，无法进行Nike登录`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始Nike登录认证...`);

        try {
            const response = await this.makeHttpRequest('POST', `${this.nikeConfig.wechatApiUrl}/wechat_auth/token/v1`, {
                appId: this.nikeConfig.appId,
                code: this.wxCode
            });

            if (response && (response.accessToken || response.access_token)) {
                this.nikeAuth.accessToken = response.accessToken || response.access_token;
                this.nikeAuth.refreshToken = response.refreshToken || response.refresh_token;
                this.nikeAuth.userId = response.user_id || response.upmId || this.extractUserIdFromToken(response.accessToken || response.access_token);
                this.nikeAuth.expiresIn = response.expiresIn || response.expires_in;

                if (isDebug) {
                    console.log(`[DEBUG] Nike登录成功`);
                    console.log(`[DEBUG] 初始User ID: ${this.nikeAuth.userId}`);
                    console.log(`[DEBUG] Token过期时间: ${this.nikeAuth.expiresIn}秒`);
                }

                // 尝试获取Nike真实用户ID
                const realUserId = await this.getNikeRealUserId();
                if (realUserId) {
                    if (isDebug) console.log(`[DEBUG] 已更新为Nike真实用户ID: ${realUserId}`);
                } else {
                    if (isDebug) console.log(`[DEBUG] 未能获取Nike真实用户ID，使用当前ID: ${this.nikeAuth.userId}`);
                }

                return true;
            } else {
                console.log(`❌ Nike登录失败: 响应格式错误`);
                if (isDebug) console.log(`[DEBUG] 响应内容: ${JSON.stringify(response)}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Nike登录失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情: ${error.stack}`);
            return false;
        }
    }

    // 从token中提取用户ID
    extractUserIdFromToken(token) {
        try {
            const tokenParts = token.split('.');
            if (tokenParts.length >= 2) {
                const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
                return payload.openId || payload.sub || payload.user_id;
            }
        } catch (e) {
            if (isDebug) console.log(`[DEBUG] 解析token失败: ${e.message}`);
        }
        return null;
    }

    // 获取Nike真实用户ID
    async getNikeRealUserId() {
        if (!this.nikeAuth.accessToken) {
            console.log(`❌ 缺少Nike认证信息，无法获取用户ID`);
            return null;
        }

        if (isDebug) console.log(`[DEBUG] 获取Nike真实用户ID...`);

        try {
            // 尝试调用用户信息API
            const userInfoUrl = `https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2`;

            const response = await this.makeHttpRequest('GET', userInfoUrl, null, {
                'host': 'wechat.nike.com.cn',
                'Accept': 'application/json',
                'App-Id': 'wechat:mp:wx096c43d1829a7788',
                'Content-Type': 'application/json; charset=UTF-8',
                'Authorization': `Bearer ${this.nikeAuth.accessToken}`,
                'nike-api-caller-id': 'nike:wechat:web:1.0',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61'
            });

            if (response && response.statusCode !== 401) {
                // 在响应中查找UUID格式的用户ID
                const responseStr = JSON.stringify(response);
                const uuidMatch = responseStr.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g);

                if (uuidMatch && uuidMatch.length > 0) {
                    const realUserId = uuidMatch[0];
                    if (isDebug) console.log(`[DEBUG] 找到Nike真实用户ID: ${realUserId}`);

                    // 更新用户ID
                    this.nikeAuth.userId = realUserId;
                    this.saveTokenCache(); // 保存更新后的用户ID

                    return realUserId;
                } else {
                    if (isDebug) console.log(`[DEBUG] 未在响应中找到UUID格式的用户ID`);
                    if (isDebug) console.log(`[DEBUG] 响应内容: ${responseStr.substring(0, 500)}...`);
                }
            } else {
                if (isDebug) console.log(`[DEBUG] 获取用户信息失败: ${JSON.stringify(response)}`);
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 获取Nike真实用户ID异常: ${error.message}`);
        }

        return null;
    }

    // HTTP请求方法
    async makeHttpRequest(method, url, data = null, headers = {}, isFormData = false) {
        const https = require('https');
        const http = require('http');
        const querystring = require('querystring');
        const urlParse = require('url').parse;

        return new Promise((resolve, reject) => {
            const parsedUrl = urlParse(url);
            const isHttps = parsedUrl.protocol === 'https:';
            const client = isHttps ? https : http;

            let postData = '';
            if (data) {
                if (isFormData) {
                    postData = querystring.stringify(data);
                } else {
                    postData = JSON.stringify(data);
                }
            }

            const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || (isHttps ? 443 : 80),
                path: parsedUrl.path,
                method: method,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'Accept': 'application/json',
                    'Content-Type': isFormData ? 'application/x-www-form-urlencoded' : 'application/json; charset=UTF-8',
                    'xweb_xhr': '1',
                    'sec-fetch-site': 'cross-site',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-dest': 'empty',
                    'referer': 'https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html',
                    'accept-encoding': 'gzip, deflate, br',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'priority': 'u=1, i',
                    ...headers
                }
            };

            if (postData) {
                options.headers['Content-Length'] = Buffer.byteLength(postData);
            }

            const req = client.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonResponse = JSON.parse(responseData);
                        jsonResponse.statusCode = res.statusCode;
                        resolve(jsonResponse);
                    } catch (parseError) {
                        resolve({
                            statusCode: res.statusCode,
                            data: responseData,
                            error: 'JSON_PARSE_ERROR'
                        });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            if (postData) {
                req.write(postData);
            }

            req.end();
        });
    }

    // Nike每日签到 - 使用HTTP请求
    async nikeDailySignIn() {
        if (!this.nikeAuth.accessToken || !this.nikeAuth.userId) {
            console.log(`❌ 缺少Nike认证信息，无法签到`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始Nike每日签到...`);

        try {
            // 构造云函数调用数据
            const cloudFunctionData = {
                api_name: "webapi_complete_daily_sign",
                data: {
                    path: `/onemp/redeem/complete_daily_sign/v2/${this.nikeAuth.userId}`,
                    method: "GET",
                    header: {
                        "Authorization": `Bearer ${this.nikeAuth.accessToken}`,
                        "App-Id": this.nikeConfig.appId,
                        "Accept": "application/json",
                        "Content-Type": "application/json; charset=UTF-8",
                        "nike-api-caller-id": "nike:wechat:web:1.0"
                    }
                },
                env: 1,
                operate_directly: false,
                tid: Date.now() * 1000000 + Math.floor(Math.random() * 1000000),
                with_credentials: true
            };

            if (isDebug) {
                console.log(`[DEBUG] 签到路径: /onemp/redeem/complete_daily_sign/v2/${this.nikeAuth.userId}`);
                console.log(`[DEBUG] 云函数数据:`, JSON.stringify(cloudFunctionData, null, 2));
            }

            // 使用wxcode调用云函数
            const result = await wxcode.getUserInfo(this.wxid, this.appid, JSON.stringify(cloudFunctionData));

            if (result && result.success) {
                if (isDebug) console.log(`[DEBUG] 云函数调用成功`);

                try {
                    // 云函数返回的数据在 result.rawData 中
                    const signInData = result.rawData;
                    if (isDebug) console.log(`[DEBUG] 签到响应数据: ${JSON.stringify(signInData)}`);

                    if (signInData && signInData.points !== undefined) {
                        const points = signInData.points || 0;
                        console.log(`🎉 Nike签到成功! 获得 ${points} 积分`);
                        return { success: true, points: points, data: signInData };
                    } else if (signInData && signInData.code === 0) {
                        console.log(`🎉 Nike签到成功!`);
                        return { success: true, points: 0, data: signInData };
                    } else {
                        console.log(`❌ Nike签到失败: ${JSON.stringify(signInData)}`);
                        return { success: false, error: 'SIGN_IN_FAILED', data: signInData };
                    }
                } catch (parseError) {
                    console.log(`❌ 解析签到响应失败: ${parseError.message}`);
                    if (isDebug) console.log(`[DEBUG] 原始响应: ${JSON.stringify(result)}`);
                    return { success: false, error: 'PARSE_ERROR', message: parseError.message };
                }
            } else {
                console.log(`❌ 签到云函数调用失败`);
                if (isDebug) console.log(`[DEBUG] 云函数响应: ${JSON.stringify(result)}`);
                return { success: false, error: 'CLOUD_FUNCTION_FAILED' };
            }
        } catch (error) {
            console.log(`❌ Nike签到异常: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 签到错误详情: ${error.stack}`);
            return { success: false, error: 'EXCEPTION', message: error.message };
        }
    }

    // 浏览商品分类任务 - 使用HTTP请求
    async browseCategoryTask() {
        if (!this.nikeAuth.accessToken || !this.nikeAuth.userId) {
            console.log(`❌ 缺少Nike认证信息，无法执行浏览任务`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始执行浏览商品分类任务...`);

        try {
            // 1. 先获取商品分类列表（模拟浏览行为）
            const categoriesResponse = await this.makeHttpRequest('GET',
                `${this.nikeConfig.wechatApiUrl}/onemp/menu_categories/v1`,
                null,
                {
                    'host': 'wechat.nike.com.cn',
                    'authorization': `Bearer ${this.nikeAuth.accessToken}`,
                    'app-id': this.nikeConfig.appId,
                    'accept': 'application/json',
                    'content-type': 'application/json; charset=UTF-8'
                }
            );

            if (categoriesResponse && categoriesResponse.statusCode === 200) {
                if (isDebug) console.log(`[DEBUG] 获取商品分类成功`);

                // 2. 等待5秒（满足浏览时间要求）
                if (isDebug) console.log(`[DEBUG] 模拟浏览5秒...`);
                await new Promise(resolve => setTimeout(resolve, 5000));

                // 3. 完成浏览任务 - 使用云函数调用
                const completeTaskData = {
                    api_name: "webapi_complete_stroll_task",
                    data: {
                        path: `/onemp/redeem/complete_stroll_task/v2/${this.nikeAuth.userId}`,
                        method: "GET",
                        header: {
                            "Authorization": `Bearer ${this.nikeAuth.accessToken}`,
                            "App-Id": this.nikeConfig.appId,
                            "Accept": "application/json",
                            "Content-Type": "application/json; charset=UTF-8",
                            "nike-api-caller-id": "nike:wechat:web:1.0"
                        }
                    },
                    env: 1,
                    operate_directly: false,
                    tid: Date.now() * 1000000 + Math.floor(Math.random() * 1000000),
                    with_credentials: true
                };

                const taskResult = await wxcode.getUserInfo(this.wxid, this.appid, JSON.stringify(completeTaskData));

                if (taskResult && taskResult.success) {
                    try {
                        // 云函数返回的数据在 taskResult.rawData 中
                        const taskData = taskResult.rawData;
                        if (isDebug) console.log(`[DEBUG] 浏览任务响应数据: ${JSON.stringify(taskData)}`);

                        if (taskData && taskData.points !== undefined) {
                            const points = taskData.points || 0;
                            console.log(`🎉 浏览任务完成! 获得 ${points} 积分`);
                            return { success: true, points: points, data: taskData };
                        } else if (taskData && taskData.code === 0) {
                            console.log(`🎉 浏览任务完成!`);
                            return { success: true, points: 0, data: taskData };
                        } else {
                            console.log(`❌ 浏览任务失败: ${JSON.stringify(taskData)}`);
                            return { success: false, data: taskData };
                        }
                    } catch (parseError) {
                        console.log(`❌ 解析浏览任务响应失败: ${parseError.message}`);
                        if (isDebug) console.log(`[DEBUG] 原始响应: ${JSON.stringify(taskResult)}`);
                        return { success: false, error: parseError.message };
                    }
                } else {
                    console.log(`❌ 浏览任务云函数调用失败`);
                    if (isDebug) console.log(`[DEBUG] 云函数响应: ${JSON.stringify(taskResult)}`);
                    return { success: false };
                }
            } else {
                console.log(`❌ 获取商品分类失败: HTTP ${categoriesResponse.statusCode}`);
                return { success: false, data: categoriesResponse };
            }
        } catch (error) {
            console.log(`❌ 浏览任务异常: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 浏览任务错误详情: ${error.stack}`);
            return { success: false, error: error.message };
        }
    }

    // 获取Nike积分信息 - 使用云函数调用
    async getNikePoints() {
        if (!this.nikeAuth.accessToken) {
            return null;
        }

        try {
            const pointsData = {
                api_name: "webapi_get_points",
                data: {
                    path: "/onemp/redeem/user_points_at_time/v2",
                    method: "GET",
                    header: {
                        "Authorization": `Bearer ${this.nikeAuth.accessToken}`,
                        "App-Id": this.nikeConfig.appId,
                        "Accept": "application/json",
                        "Content-Type": "application/json; charset=UTF-8",
                        "nike-api-caller-id": "nike:wechat:web:1.0"
                    }
                },
                env: 1,
                operate_directly: false,
                tid: Date.now() * 1000000 + Math.floor(Math.random() * 1000000),
                with_credentials: true
            };

            const result = await wxcode.getUserInfo(this.wxid, this.appid, JSON.stringify(pointsData));

            if (result && result.success) {
                try {
                    const pointsInfo = JSON.parse(result.rawData.data);
                    if (pointsInfo && pointsInfo.data) {
                        const points = pointsInfo.data.points || pointsInfo.data.point_value || 0;
                        if (isDebug) console.log(`[DEBUG] 当前积分: ${points}`);
                        return points;
                    } else if (pointsInfo && pointsInfo.points !== undefined) {
                        const points = pointsInfo.points;
                        if (isDebug) console.log(`[DEBUG] 当前积分: ${points}`);
                        return points;
                    }
                } catch (parseError) {
                    if (isDebug) console.log(`[DEBUG] 解析积分信息失败: ${parseError.message}`);
                    if (isDebug) console.log(`[DEBUG] 原始响应: ${JSON.stringify(result.rawData)}`);
                }
            } else {
                if (isDebug) console.log(`[DEBUG] 积分云函数调用失败: ${JSON.stringify(result)}`);
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 获取积分信息异常: ${error.message}`);
        }

        return null;
    }

    // 获取任务中心信息 - 使用HTTP请求
    async getRedeemCenterInfo() {
        if (!this.nikeAuth.accessToken) {
            return null;
        }

        try {
            const response = await this.makeHttpRequest('GET',
                `${this.nikeConfig.wechatApiUrl}/onemp/redeem/redeem_center_info/v2`,
                null,
                {
                    'host': 'wechat.nike.com.cn',
                    'authorization': `Bearer ${this.nikeAuth.accessToken}`,
                    'app-id': this.nikeConfig.appId,
                    'accept': 'application/json',
                    'content-type': 'application/json; charset=UTF-8'
                }
            );

            if (response && response.statusCode === 200) {
                if (isDebug) console.log(`[DEBUG] 获取任务中心信息成功`);
                return response;
            } else {
                if (isDebug) console.log(`[DEBUG] 获取任务中心信息失败: HTTP ${response.statusCode}`);
                if (isDebug) console.log(`[DEBUG] 任务中心响应: ${JSON.stringify(response)}`);
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 获取任务中心信息异常: ${error.message}`);
        }

        return null;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. Nike业务逻辑
            console.log(`🚀 开始执行Nike任务...`);
            console.log(`📱 用户信息: ${this.userProfile?.nickName || '未知用户'}`);

            // Nike登录认证
            console.log(`🔐 开始Nike登录认证...`);
            if (!await this.nikeLogin()) {
                console.log(`❌ Nike登录失败，脚本终止`);
                return;
            }

            console.log(`✅ Nike登录成功`);

            // 获取当前积分
            console.log(`📊 获取当前积分...`);
            const currentPoints = await this.getNikePoints();
            if (currentPoints !== null) {
                console.log(`💎 当前积分: ${currentPoints}`);
            }

            // 执行每日签到
            console.log(`📅 执行每日签到...`);
            const signInResult = await this.nikeDailySignIn();
            if (signInResult && signInResult.success) {
                if (signInResult.points > 0) {
                    console.log(`🎉 签到成功，获得 ${signInResult.points} 积分`);
                } else {
                    console.log(`✅ 签到成功`);
                }
            } else {
                console.log(`⚠️ 签到失败或已签到`);
            }

            // 执行浏览任务
            console.log(`👀 执行浏览商品分类任务...`);
            const browseResult = await this.browseCategoryTask();
            if (browseResult && browseResult.success) {
                if (browseResult.points > 0) {
                    console.log(`🎉 浏览任务完成，获得 ${browseResult.points} 积分`);
                } else {
                    console.log(`✅ 浏览任务完成`);
                }
            } else {
                console.log(`⚠️ 浏览任务失败或已完成`);
            }

            // 获取任务中心信息
            console.log(`📋 获取任务中心信息...`);
            const centerInfo = await this.getRedeemCenterInfo();
            if (centerInfo) {
                console.log(`📋 任务中心信息获取成功`);
            }

            // 再次获取积分，查看变化
            console.log(`📊 获取最新积分...`);
            const finalPoints = await this.getNikePoints();
            if (finalPoints !== null) {
                console.log(`💎 最终积分: ${finalPoints}`);
                if (currentPoints !== null) {
                    const earned = finalPoints - currentPoints;
                    if (earned > 0) {
                        console.log(`🎊 本次共获得 ${earned} 积分`);
                    }
                }
            }

            console.log(`✅ Nike脚本执行完成`);

            // 保存更新后的缓存（包含Nike认证信息）
            this.saveTokenCache();
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 Nike耐克小程序签到和任务脚本开始执行`);
    console.log(`🎯 功能：每日签到、浏览任务、积分查询`);
    console.log(`📅 版本：v2.0 - ${new Date().toLocaleString()}`);

    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const nikeScript = new NikeScript(wxid);
            await nikeScript.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
