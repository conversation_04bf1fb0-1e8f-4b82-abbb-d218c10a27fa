{"log": {"version": "1.2", "creator": {"name": "Reqable", "version": "2.33.5"}, "entries": [{"startedDateTime": "2025-08-01T03:36:05.475Z", "time": 372, "request": {"method": "POST", "url": "https://insights.nike.com/v1/accounts/1015810/events", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":authority", "value": "insights.nike.com"}, {"name": ":path", "value": "/v1/accounts/1015810/events"}, {"name": ":scheme", "value": "https"}, {"name": "content-length", "value": "1728"}, {"name": "xweb_xhr", "value": "1"}, {"name": "x-insert-key", "value": "D6zkf-3hVEO4bIR7I9o6sivPcju2hLNT"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185"}, {"name": "content-type", "value": "application/json"}, {"name": "accept", "value": "*/*"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "referer", "value": "https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "priority", "value": "u=1, i"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "[{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019364994,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0801_007-1.png\",\"loadTime\":42,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019364996,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0801_006-1.png\",\"loadTime\":43,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019364997,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_95-1.png\",\"loadTime\":44,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019364998,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_96-1.png\",\"loadTime\":45,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019364998,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_97-1.png\",\"loadTime\":14,\"traceName\":\"default\"}]"}, "headersSize": 716, "bodySize": 1728, "_status": "completed", "_startTimestamp": 1754019365475431, "_endTimestamp": 1754019365476902}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "content-length", "value": "63"}, {"name": "content-type", "value": "text/json; charset=utf-8"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "*"}, {"name": "x-served-by", "value": "cache-tyo11952-TYO"}, {"name": "date", "value": "Fri, 01 Aug 2025 03:36:05 GMT"}], "content": {"size": 63, "mimeType": "text/json", "text": "{\"success\":true, \"uuid\":\"6442c6c7-0001-b21b-b309-019863b382e0\"}"}, "redirectURL": "", "headersSize": 303, "bodySize": 63, "_status": "completed", "_startTimestamp": 1754019365846481, "_endTimestamp": 1754019365847604}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "serverIPAddress": "************", "connection": "3443", "comment": "", "_id": 5057, "_uid": "614c3af2-c711-48ad-a68c-19306a745f89", "_cid": 3443, "_ctime": 1754019365018, "_sid": 1, "_stime": 1754019365018, "_serverAddress": "************", "_serverAddressFamily": 0, "_serverPort": 443, "_clientAddress": "127.0.0.1", "_clientAddressFamily": 0, "_clientPort": 45095, "_app": {"name": "WeChatAppEx", "id": "WeChatAppEx.exe", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\RadiumWMPF\\14185\\extracted\\runtime\\WeChatAppEx.exe", "stackTrace": null}}, {"startedDateTime": "2025-08-01T03:36:01.140Z", "time": 599, "request": {"method": "POST", "url": "https://insights.nike.com/v1/accounts/1015810/events", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":authority", "value": "insights.nike.com"}, {"name": ":path", "value": "/v1/accounts/1015810/events"}, {"name": ":scheme", "value": "https"}, {"name": "content-length", "value": "1800"}, {"name": "xweb_xhr", "value": "1"}, {"name": "x-insert-key", "value": "D6zkf-3hVEO4bIR7I9o6sivPcju2hLNT"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185"}, {"name": "content-type", "value": "application/json"}, {"name": "accept", "value": "*/*"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "referer", "value": "https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "priority", "value": "u=1, i"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "[{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360679,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_1000-140-1.png\",\"loadTime\":339,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360681,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_95-1.png\",\"loadTime\":342,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360692,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_800-100-1.png\",\"loadTime\":353,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360765,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://engagement-assets.nike.com.cn/nike/redeemManage/icon/Shop-200.svg\",\"loadTime\":248,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360891,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://s3.cn-northwest-1.amazonaws.com.cn/mp-static-assets.gc-uat.nike.com/one-mp/redeem/Shop - x2 - 100_100.png\",\"loadTime\":374,\"traceName\":\"default\"}]"}, "headersSize": 716, "bodySize": 1800, "_status": "completed", "_startTimestamp": 1754019361140400, "_endTimestamp": 1754019361143216}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "content-length", "value": "63"}, {"name": "content-type", "value": "text/json; charset=utf-8"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "*"}, {"name": "x-served-by", "value": "cache-tyo11952-TYO"}, {"name": "date", "value": "Fri, 01 Aug 2025 03:36:01 GMT"}], "content": {"size": 63, "mimeType": "text/json", "text": "{\"success\":true, \"uuid\":\"572f3db5-0001-bbd9-d2ff-019863b37207\"}"}, "redirectURL": "", "headersSize": 303, "bodySize": 63, "_status": "completed", "_startTimestamp": 1754019361737332, "_endTimestamp": 1754019361739411}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "serverIPAddress": "************", "connection": "3439", "comment": "", "_id": 5052, "_uid": "2c1096d7-1b34-4a12-b958-66e82b254ad5", "_cid": 3439, "_ctime": 1754019360685, "_sid": 3, "_stime": 1754019361140, "_serverAddress": "************", "_serverAddressFamily": 0, "_serverPort": 443, "_clientAddress": "127.0.0.1", "_clientAddressFamily": 0, "_clientPort": 45082, "_app": {"name": "WeChatAppEx", "id": "WeChatAppEx.exe", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\RadiumWMPF\\14185\\extracted\\runtime\\WeChatAppEx.exe", "stackTrace": null}}, {"startedDateTime": "2025-08-01T03:36:01.139Z", "time": 597, "request": {"method": "POST", "url": "https://insights.nike.com/v1/accounts/1015810/events", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":authority", "value": "insights.nike.com"}, {"name": ":path", "value": "/v1/accounts/1015810/events"}, {"name": ":scheme", "value": "https"}, {"name": "content-length", "value": "1737"}, {"name": "xweb_xhr", "value": "1"}, {"name": "x-insert-key", "value": "D6zkf-3hVEO4bIR7I9o6sivPcju2hLNT"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185"}, {"name": "content-type", "value": "application/json"}, {"name": "accept", "value": "*/*"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "referer", "value": "https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "priority", "value": "u=1, i"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "[{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360518,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0801_007-1.png\",\"loadTime\":179,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360623,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_500-50-1.png\",\"loadTime\":284,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360624,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_97-1.png\",\"loadTime\":285,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360644,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0701_96-1.png\",\"loadTime\":305,\"traceName\":\"default\"},{\"wechatVersion\":\"3.9.12\",\"mpSDKVersion\":\"3.8.12\",\"os\":\"Windows 10 x64\",\"model\":\"microsoft\",\"platform\":\"windows\",\"mpVersion\":\"v5.3.0\",\"buildType\":\"production\",\"timestamp\":1754019360676,\"eventType\":\"wechat_client_reported_image_load\",\"route\":\"https://mp-static-assets.gc.nike.com/one-mp/redeem/0801_006-1.png\",\"loadTime\":337,\"traceName\":\"default\"}]"}, "headersSize": 716, "bodySize": 1737, "_status": "completed", "_startTimestamp": 1754019361139296, "_endTimestamp": 1754019361142637}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "content-length", "value": "63"}, {"name": "content-type", "value": "text/json; charset=utf-8"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "*"}, {"name": "x-served-by", "value": "cache-tyo11928-TYO"}, {"name": "date", "value": "Fri, 01 Aug 2025 03:36:01 GMT"}], "content": {"size": 63, "mimeType": "text/json", "text": "{\"success\":true, \"uuid\":\"77676c27-0001-b7d8-af48-019863b37205\"}"}, "redirectURL": "", "headersSize": 303, "bodySize": 63, "_status": "completed", "_startTimestamp": 1754019361735251, "_endTimestamp": 1754019361736418}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "serverIPAddress": "************", "connection": "3439", "comment": "", "_id": 5049, "_uid": "173773cf-616d-460a-90bd-614e831cdb3f", "_cid": 3439, "_ctime": 1754019360685, "_sid": 1, "_stime": 1754019360685, "_serverAddress": "************", "_serverAddressFamily": 0, "_serverPort": 443, "_clientAddress": "127.0.0.1", "_clientAddressFamily": 0, "_clientPort": 45082, "_app": {"name": "WeChatAppEx", "id": "WeChatAppEx.exe", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\RadiumWMPF\\14185\\extracted\\runtime\\WeChatAppEx.exe", "stackTrace": null}}]}}