/**
 * Nike直接API调用测试
 * 基于您提供的真实抓包数据
 */

const axios = require('axios');
const fs = require('fs');

// 从您的抓包数据中获取的真实信息
const REAL_USER_ID = "2dfac020-b3da-4cee-ae0b-977eac128709";
const REAL_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgwNWIyZjBiLTgyMjktNGVhZi04MmJhLTA0NDZkOGQ0ODk0YnNpZyJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LKorjqhaR1nwmph1dOro7mg4Qcswv5bY92kLzZBg2ATYqqnUpsuqMjHHVsRK_txTAHzJyzQ8si670U1iDdyh2BzlfqMJnkPy8rukevUL_iSf1cNlHYSIKsYaIbdtHWFPMraEUIdxj4kXw7vNjrhFrwkPOo2mRV-2tIRCVYJPgXnxEcNSbZavlCYjdT3uiQO8garJZrnpUXO9ni-UhVCdN9RzonckmCQq-i6L4oB5Gb9YEHkQafAUlXIG2JTaTQqTOmeIihVKjg8VisXhkW7h2MEy6px-N_5qJ7aII6qSjehP68weP8zO6vKrGmIm0iRtBLCkMKzZiM2Tt8D3WJ_biw";

async function testDirectNikeAPI() {
    console.log('🎯 Nike直接API调用测试');
    console.log('=====================================');
    console.log(`👤 用户ID: ${REAL_USER_ID}`);
    console.log(`🔐 Token: ${REAL_TOKEN.substring(0, 50)}...`);
    console.log('=====================================\n');

    // 测试1: 使用真实的抓包数据
    console.log('🔄 测试1: 使用真实抓包数据...');
    try {
        const response = await axios.get(
            `https://wechat.nike.com.cn/onemp/redeem/complete_daily_sign/v2/${REAL_USER_ID}`,
            {
                headers: {
                    "host": "wechat.nike.com.cn",
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Authorization": `Bearer ${REAL_TOKEN}`
                },
                timeout: 15000,
                validateStatus: () => true
            }
        );

        console.log(`📊 响应状态: ${response.status}`);

        // 检查响应类型
        if (typeof response.data === 'string' && response.data.includes('<html>')) {
            console.log(`📥 响应类型: HTML页面 (长度: ${response.data.length})`);
            console.log(`📄 这是一个网页，不是API响应`);
        } else {
            console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
        }

        if (response.status === 200 && response.data.points !== undefined) {
            console.log(`🎉 签到成功! 获得 ${response.data.points} 积分`);
            return { success: true, points: response.data.points };
        } else {
            console.log(`❌ 签到失败: ${response.status} - ${JSON.stringify(response.data)}`);
        }
    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        if (error.response) {
            console.log(`📊 错误状态: ${error.response.status}`);
            console.log(`📊 错误数据:`, error.response.data);
        }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试2: 尝试获取用户信息API
    console.log('🔄 测试2: 获取用户信息...');
    try {
        const response = await axios.get(
            'https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2',
            {
                headers: {
                    "host": "wechat.nike.com.cn",
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Authorization": `Bearer ${REAL_TOKEN}`
                },
                timeout: 15000,
                validateStatus: () => true
            }
        );

        console.log(`📊 响应状态: ${response.status}`);
        console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));

        // 查找UUID格式的用户ID
        const responseStr = JSON.stringify(response.data);
        const uuidMatch = responseStr.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g);
        if (uuidMatch) {
            console.log(`🎯 找到UUID用户ID: ${uuidMatch}`);
        }

    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试3: 使用当前脚本的Token
    console.log('🔄 测试3: 使用当前脚本Token...');
    try {
        const tokens = JSON.parse(fs.readFileSync('./nike_tokens.json', 'utf8'));
        const currentAuth = tokens['wxid_ltkystdcspc822'].nikeAuth;
        
        console.log(`🔐 当前Token: ${currentAuth.accessToken.substring(0, 50)}...`);
        console.log(`👤 当前用户ID: ${currentAuth.userId}`);

        // 尝试用当前Token调用真实用户ID的签到API
        const response = await axios.get(
            `https://wechat.nike.com.cn/onemp/redeem/complete_daily_sign/v2/${REAL_USER_ID}`,
            {
                headers: {
                    "host": "wechat.nike.com.cn",
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Authorization": `Bearer ${currentAuth.accessToken}`
                },
                timeout: 15000,
                validateStatus: () => true
            }
        );

        console.log(`📊 响应状态: ${response.status}`);
        console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        if (error.response) {
            console.log(`📊 错误状态: ${error.response.status}`);
            console.log(`📊 错误数据:`, error.response.data);
        }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试4: 尝试不同的API端点
    console.log('🔄 测试4: 尝试其他API端点...');
    const endpoints = [
        'https://api.nike.com.cn/onemp/redeem/complete_daily_sign/v2/' + REAL_USER_ID,
        'https://nike.com.cn/onemp/redeem/complete_daily_sign/v2/' + REAL_USER_ID,
        'https://wechat.nike.com.cn/redeem/complete_daily_sign/v2/' + REAL_USER_ID
    ];

    for (const endpoint of endpoints) {
        console.log(`🔗 测试端点: ${endpoint}`);
        try {
            const response = await axios.get(endpoint, {
                headers: {
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Authorization": `Bearer ${REAL_TOKEN}`,
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61"
                },
                timeout: 10000,
                validateStatus: () => true
            });

            console.log(`  📊 状态: ${response.status}`);
            if (response.status === 200) {
                console.log(`  ✅ 成功! 数据:`, JSON.stringify(response.data, null, 2));
                break;
            } else {
                console.log(`  ❌ 失败: ${JSON.stringify(response.data)}`);
            }
        } catch (error) {
            console.log(`  ❌ 异常: ${error.message}`);
        }
    }
}

// 执行测试
if (require.main === module) {
    testDirectNikeAPI().catch(error => {
        console.log('❌ 测试脚本执行出错:', error.message);
        console.error(error.stack);
    });
}

module.exports = { testDirectNikeAPI };
