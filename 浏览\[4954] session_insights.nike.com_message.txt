POST /v1/accounts/1015810/events h2
host: insights.nike.com
content-length: 3379
xweb_xhr: 1
x-insert-key: D6zkf-3hVEO4bIR7I9o6sivPcju2hLNT
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
content-type: application/json
accept: */*
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1, i

[{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":*************,"req_uri":"https://api.nike.com.cn/product_feed/threads/v3?filter=marketplace%28CN%29&filter=language%28zh-Hans%29&filter=channelId%2815d92135-394e-487a-8afa-f6c8525c9ea9%29&filter=exclusiveAccess%28true%2Cfalse%29&filter=publishedContent.properties.publish.collections%282664717d-5ee8-4dbe-871c-d8e5b1311636%29&count=100","req_host":"https://api.nike.com.cn","req_path":"/product_feed/threads/v3","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","req_header_nike-api-caller-id":"nike:wechat:web:1.0","res_status":200,"trackId":"fed3c629-1924-4c93-9ab2-65f0a075b578","networkType":"wifi","roundTripTime":0.314,"eventType":"wechatmp_client_api_performance"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019319625,"req_uri":"https://api.nike.com.cn/product_feed/threads/v3?filter=marketplace%28CN%29&filter=language%28zh-Hans%29&filter=channelId%2815d92135-394e-487a-8afa-f6c8525c9ea9%29&filter=exclusiveAccess%28true%2Cfalse%29&filter=id%28fd037fb3-df2b-43c2-ade1-357fe31b9938%29","req_host":"https://api.nike.com.cn","req_path":"/product_feed/threads/v3","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","req_header_nike-api-caller-id":"nike:wechat:web:1.0","res_status":200,"trackId":"9a1094a2-1dc8-4c14-b0de-d4d676103674","networkType":"wifi","roundTripTime":0.232,"eventType":"wechatmp_client_api_performance"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019321025,"req_uri":"https://api.nike.com.cn/product_feed/threads/v3?filter=marketplace%28CN%29&filter=language%28zh-Hans%29&filter=channelId%2815d92135-394e-487a-8afa-f6c8525c9ea9%29&filter=exclusiveAccess%28true%2Cfalse%29&filter=id%2861ea504e-153a-45a6-8f08-af2e455bdb42%29","req_host":"https://api.nike.com.cn","req_path":"/product_feed/threads/v3","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","req_header_nike-api-caller-id":"nike:wechat:web:1.0","res_status":200,"trackId":"038e51f5-7a42-40ba-bc78-d9aff0d4dc19","networkType":"wifi","roundTripTime":0.218,"eventType":"wechatmp_client_api_performance"},{"wechatVersion":"3.9.12","mpSDKVersion":"3.8.12","os":"Windows 10 x64","model":"microsoft","platform":"windows","mpVersion":"v5.3.0","buildType":"production","timestamp":1754019321209,"req_uri":"https://api.nike.com.cn/product_feed/threads/v3?filter=marketplace%28CN%29&filter=language%28zh-Hans%29&filter=channelId%2815d92135-394e-487a-8afa-f6c8525c9ea9%29&filter=exclusiveAccess%28true%2Cfalse%29&filter=publishedContent.properties.publish.collections%288e2e5a6b-bff7-4ea8-a6c9-e7f748c67273%29&count=100","req_host":"https://api.nike.com.cn","req_path":"/product_feed/threads/v3","req_method":"GET","req_header_content-type":"application/json; charset=UTF-8","req_header_nike-api-caller-id":"nike:wechat:web:1.0","res_status":200,"trackId":"727acd08-35da-4fe8-a9b6-3f28354455cc","networkType":"wifi","roundTripTime":0.401,"eventType":"wechatmp_client_api_performance"}]

h2 200
content-length: 63
content-type: text/json; charset=utf-8
nr-rate-limited: allowed
access-control-allow-methods: GET, POST, PUT, HEAD, OPTIONS
access-control-allow-credentials: true
access-control-allow-origin: *
x-served-by: cache-tyo11928-TYO
date: Fri, 01 Aug 2025 03:35:31 GMT

{"success":true, "uuid":"02a044fa-0001-b64b-9a26-019863b2fd16"}