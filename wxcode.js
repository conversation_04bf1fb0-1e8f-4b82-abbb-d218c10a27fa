/**
 * 微信授权相关功能模块
 * 1.0.0版本
 */
const request = require('request');
let headers = { "Content-Type": "application/json" }
let xieyi = process.env.PHONECODE_SERVER || 'http://820121.xyz:8800';
let xueyi = xieyi + '/api/';
process.env.LOGS = 0;
// 获取小程序的code
async function getWxCode(wxid, appid) {
    let data = await task(
        'post',
        `${xueyi}Wxapp/JSLogin`,
        headers,
        `{"wxid": "${wxid}","appid": "${appid}"}`
    );

    if (data.Code == 0) {
        return {
            success: true,
            code: data.Data.code,
            appid: appid
        };
    } else {
        return {
            success: false,
            error: data.Message || '获取微信授权码失败'
        };
    }
}
// 获取phonecode
async function getmobile(wxid, appid) {
    const body = JSON.stringify({
        "wxid": wxid,
        "appid": appid,
        "data": "{\"api_name\":\"webapi_getuserwxphone\",\"with_credentials\":true}",
        "opt": 1
    });

    let data = await task(
        'post',
        `${xueyi}Wxapp/GetAllMobile`,
        headers,
        body
    );

    if (data.Code == 0) {
        return {
            success: true,
            encryptedData: data.Data.ALLMobile[0].encryptedData,
            iv: data.Data.ALLMobile[0].iv,
            code: data.Data.ALLMobile[0].code
        };
    } else {
        return {
            success: false,
            error: data.Message || '获取加密数据失败'
        };
    }
}
// 调用小程序的云函数
async function getUserInfo(wxid, appid, str) {
    const body = JSON.stringify({
        "wxid": wxid,
        "appid": appid,
        "data":str
    });

    let data = await task(
        'post', 
        `${xueyi}Wxapp/CloudCallFunction`, 
        headers, 
        body
    );

    if (data.Code == 0) {
        try {
            let res = JSON.parse(Buffer.from(data.Data.data, 'base64').toString());
            return {
                success: true,
                signature: res.signature,
                encryptedData: res.encryptedData,
                iv: res.iv,
                rawData: res
            };
        } catch (e) {
            return {
                success: false,
                error: '解析用户信息失败: ' + e.message
            };
        }
    } else {
        return {
            success: false,
            error: data.Message || '获取用户信息失败'
        };
    }
}

// 获取用户openid
async function getOpenid(wxid, appid) {

    const body = JSON.stringify({
        "wxid": wxid,
        "appid": appid,
        "towxid": wxid
    });

    let data = await task(
        'post',
        `${xueyi}Wxapp/GetUserOpenId`,
        headers,
        body
    );

    if (data.Code == 0) {
        return {
            success: true,
            openid: data.Data.Openid
        };
    } else {
        return {
            success: false,
            error: data.Message || '获取openid失败'
        };
    }
}
// 获取APP code
async function getAppCode(wxid, appid) {

    const body = JSON.stringify({
        "wxid": wxid,
        "appid": appid,
        "url": ""
    });

    let data = await task(
        'post',
        `${xueyi}tools/thrid/app/grant`,
        headers,
        body
    );

    if (data.Code == 0) {
        return {
            success: true,
            code: data.Data
        };
    } else {
        return {
            success: false,
            error: data.Message || '获取APP code失败'
        };
    }
}
function task(method, taskurl, taskheader, taskbody, taskhost) {
    if (method == 'delete') {
        method = method.toUpperCase();
    }

    if (method == 'post') {
        delete taskheader['content-type'];
        delete taskheader['Content-type'];
        delete taskheader['content-Type'];

        if (safeGet(taskbody)) {
            taskheader['Content-Type'] = 'application/json;charset=UTF-8';
        } else {
            taskheader['Content-Type'] = 'application/x-www-form-urlencoded';
        }

        if (taskbody) {
            taskheader['Content-Length'] = lengthInUtf8Bytes(taskbody);
        }
    }

    if (method == 'get') {
        delete taskheader['content-type'];
        delete taskheader['Content-type'];
        delete taskheader['content-Type'];
        delete taskheader['Content-Length'];
    }

    taskheader['Host'] = taskurl.replace('//', '/').split('/')[1];

    return new Promise(async resolve => {
        var httpget = {
            url: taskurl,
            headers: taskheader,
            body: method.indexOf('T') < 0 ? taskbody : undefined,
            form: method.indexOf('T') >= 0 ? JSON.parse(taskbody) : undefined,
            proxy: taskhost ? 'http://' + taskhost : undefined
        };

        request[method.toLowerCase()](httpget, (err, response, data) => {
            try {
                if (data && process.env.LOGS == 1) {
                    console.log(`================ 请求 ================`);
                    console.log(httpget);
                    console.log(`================ 返回 ================`);
                    console.log(safeGet(data) ? JSON.parse(data) : data);
                }
            } catch (e) {
                console.log(e, taskurl + '\n' + taskheader);
            } finally {
                let datas = '';
                if (!err) {
                    if (safeGet(data)) {
                        datas = JSON.parse(data);
                    } else {
                        datas = data;
                    }
                } else {
                    datas = taskurl + '   API请求失败，请检查网络重试\n' + err;
                }
                return resolve(datas);
            }
        });
    });
}
function safeGet(data) {
    try {
        if (typeof JSON.parse(data) == 'object') {
            return true;
        }
    } catch (e) {
        return false;
    }
    return false;
}
function lengthInUtf8Bytes(str) {
    let m = encodeURIComponent(str).match(/%[89ABab]/g);
    return str.length + (m ? m.length : 0);
}
// 通用HTTP请求方法（用于Nike API调用）
async function httpRequest(method, url, data = null, customHeaders = {}) {
    const headers = {
        "Accept": "application/json",
        "Content-Type": "application/json; charset=UTF-8",
        "nike-api-caller-id": "nike:wechat:web:1.0",
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003D57) NetType/WIFI Language/zh_CN",
        ...customHeaders
    };

    let body = null;
    if (data) {
        if (headers['Content-Type'].includes('application/x-www-form-urlencoded')) {
            // 表单数据
            body = Object.keys(data).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`).join('&');
        } else {
            // JSON数据
            body = JSON.stringify(data);
        }
    }

    try {
        const response = await task(method.toLowerCase(), url, headers, body);

        // 检查响应是否为错误
        if (typeof response === 'string' && response.includes('API请求失败')) {
            throw new Error(response);
        }

        return response;
    } catch (error) {
        throw error;
    }
}

module.exports = {
    task,
    safeGet,
    lengthInUtf8Bytes,
    getWxCode,
    getmobile,
    getUserInfo,
    getOpenid,
    getAppCode,
    httpRequest
};
