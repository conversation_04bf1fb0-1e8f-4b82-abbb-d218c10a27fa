/**
 * Nike小程序专业Frida Hook脚本
 * 专门Hook wx.cloud.callContainer 方法
 * 基于小程序源码深度分析
 * 作者：Tianxx
 * 版本：2.0
 */

console.log("\n🎯 Nike小程序专业Hook脚本 v2.0");
console.log("=====================================");
console.log("🔍 目标: Hook wx.cloud.callContainer");
console.log("📱 设备: iPhone微信小程序");
console.log("=====================================\n");

// 全局数据存储
var capturedData = {
    cloudCalls: [],
    signInCalls: [],
    authCalls: []
};

// 颜色输出
function log(message, color = 'white') {
    const colors = {
        red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m',
        blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m',
        white: '\x1b[37m', reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

// Hook微信云开发相关的JavaScript方法
function hookWxCloudMethods() {
    log("[🔍] 开始Hook微信云开发方法...", 'yellow');
    
    // Hook所有WebView中的JavaScript执行
    try {
        var WKWebView = ObjC.classes.WKWebView;
        if (WKWebView) {
            log("[✓] 找到WKWebView类", 'green');
            
            var evaluateJS = WKWebView['- evaluateJavaScript:completionHandler:'];
            if (evaluateJS) {
                Interceptor.attach(evaluateJS.implementation, {
                    onEnter: function(args) {
                        try {
                            var jsCode = new ObjC.Object(args[2]).toString();
                            
                            // 检查是否是云函数调用
                            if (jsCode.includes('wx.cloud.callContainer') || 
                                jsCode.includes('callContainer')) {
                                
                                log("\n[🎯] 发现云函数调用!", 'green');
                                log("[📝] JS代码: " + jsCode.substring(0, 500) + "...", 'cyan');
                                
                                // 保存云函数调用
                                capturedData.cloudCalls.push({
                                    timestamp: new Date().toISOString(),
                                    jsCode: jsCode,
                                    type: 'cloud_call'
                                });
                            }
                            
                            // 检查是否包含签到相关的调用
                            if (jsCode.includes('complete_daily_sign') || 
                                jsCode.includes('/redeem/complete_daily_sign')) {
                                
                                log("\n[🎉] 发现签到相关调用!", 'green');
                                log("[📝] 签到JS: " + jsCode, 'yellow');
                                
                                // 解析签到参数
                                var signInData = extractSignInData(jsCode);
                                if (signInData) {
                                    capturedData.signInCalls.push({
                                        timestamp: new Date().toISOString(),
                                        data: signInData,
                                        jsCode: jsCode
                                    });
                                    
                                    log("[💾] 签到数据已保存", 'blue');
                                }
                            }
                            
                            // 检查认证相关调用
                            if (jsCode.includes('wechat_auth/token') || 
                                jsCode.includes('Authorization') ||
                                jsCode.includes('Bearer')) {
                                
                                log("\n[🔐] 发现认证相关调用!", 'magenta');
                                log("[📝] 认证JS: " + jsCode.substring(0, 300) + "...", 'cyan');
                                
                                capturedData.authCalls.push({
                                    timestamp: new Date().toISOString(),
                                    jsCode: jsCode,
                                    type: 'auth_call'
                                });
                            }
                            
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                });
                
                log("[✅] WebView JavaScript Hook 已设置", 'green');
            }
        }
    } catch (e) {
        log("[-] Hook WebView失败: " + e, 'red');
    }
}

// 从JS代码中提取签到数据
function extractSignInData(jsCode) {
    try {
        // 尝试提取云函数调用的参数
        var patterns = [
            /callContainer\s*\(\s*({[^}]+})\s*\)/,
            /"path"\s*:\s*"([^"]+)"/,
            /"method"\s*:\s*"([^"]+)"/,
            /"header"\s*:\s*({[^}]+})/,
            /Authorization['"]\s*:\s*['"]([^'"]+)['"]/,
            /Bearer\s+([a-zA-Z0-9\-_.]+)/
        ];
        
        var extractedData = {};
        
        patterns.forEach((pattern, index) => {
            var match = jsCode.match(pattern);
            if (match) {
                switch(index) {
                    case 0: extractedData.containerParams = match[1]; break;
                    case 1: extractedData.path = match[1]; break;
                    case 2: extractedData.method = match[1]; break;
                    case 3: extractedData.header = match[1]; break;
                    case 4: extractedData.authorization = match[1]; break;
                    case 5: extractedData.token = match[1]; break;
                }
            }
        });
        
        return Object.keys(extractedData).length > 0 ? extractedData : null;
    } catch (e) {
        return null;
    }
}

// Hook原生网络请求作为备用
function hookNativeRequests() {
    log("[🔍] 设置原生网络请求Hook作为备用...", 'yellow');
    
    try {
        var NSURLSession = ObjC.classes.NSURLSession;
        if (NSURLSession) {
            var dataTask = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            if (dataTask) {
                Interceptor.attach(dataTask.implementation, {
                    onEnter: function(args) {
                        try {
                            var request = new ObjC.Object(args[2]);
                            var url = request.URL().absoluteString().toString();
                            
                            if (url.includes('nike.com') || url.includes('complete_daily_sign')) {
                                log("\n[🌐] 原生网络请求: " + url, 'cyan');
                                
                                // 获取请求头
                                var headers = {};
                                try {
                                    var allHeaders = request.allHTTPHeaderFields();
                                    if (allHeaders) {
                                        var keys = allHeaders.allKeys();
                                        for (var i = 0; i < keys.count(); i++) {
                                            var key = keys.objectAtIndex_(i).toString();
                                            var value = allHeaders.objectForKey_(key).toString();
                                            headers[key] = value;
                                        }
                                        log("[📋] 请求头: " + JSON.stringify(headers, null, 2), 'blue');
                                    }
                                } catch (e) {
                                    log("[-] 获取请求头失败: " + e, 'red');
                                }
                            }
                        } catch (e) {
                            // 忽略
                        }
                    }
                });
            }
        }
    } catch (e) {
        log("[-] Hook原生请求失败: " + e, 'red');
    }
}

// 内存扫描寻找关键字符串
function scanMemoryForKeys() {
    log("[🔍] 扫描内存中的关键信息...", 'yellow');
    
    setTimeout(function() {
        try {
            var ranges = Process.enumerateRanges('r--');
            var foundCount = 0;
            
            ranges.forEach(range => {
                try {
                    // 扫描 "complete_daily_sign"
                    Memory.scan(range.base, range.size, '63 6f 6d 70 6c 65 74 65 5f 64 61 69 6c 79 5f 73 69 67 6e', {
                        onMatch: function(address, size) {
                            foundCount++;
                            log("[🎯] 找到签到字符串: " + address, 'green');
                            
                            try {
                                var context = Memory.readUtf8String(address.sub(100), 300);
                                if (context.includes('callContainer') || context.includes('wx.cloud')) {
                                    log("[💡] 发现云函数上下文: " + context.substring(0, 200) + "...", 'yellow');
                                }
                            } catch (e) {
                                // 忽略
                            }
                        },
                        onError: function(reason) {
                            // 忽略扫描错误
                        }
                    });
                } catch (e) {
                    // 忽略范围扫描错误
                }
            });
            
            log("[📊] 内存扫描完成，找到 " + foundCount + " 个匹配项", 'blue');
        } catch (e) {
            log("[-] 内存扫描失败: " + e, 'red');
        }
    }, 3000);
}

// 全局函数 - 显示捕获的数据
this.showCapturedData = function() {
    log("\n📊 捕获数据统计", 'cyan');
    log("=====================================", 'white');
    log("云函数调用: " + capturedData.cloudCalls.length, 'green');
    log("签到调用: " + capturedData.signInCalls.length, 'green');
    log("认证调用: " + capturedData.authCalls.length, 'green');
    log("=====================================\n", 'white');
    
    if (capturedData.signInCalls.length > 0) {
        log("🎯 最新签到调用数据:", 'yellow');
        var latest = capturedData.signInCalls[capturedData.signInCalls.length - 1];
        console.log(JSON.stringify(latest, null, 2));
    }
    
    return capturedData;
};

// 全局函数 - 导出签到API调用
this.exportSignInAPI = function() {
    if (capturedData.signInCalls.length === 0) {
        log("❌ 暂无签到调用数据", 'red');
        return null;
    }
    
    var signInCall = capturedData.signInCalls[capturedData.signInCalls.length - 1];
    var apiCall = {
        timestamp: signInCall.timestamp,
        method: signInCall.data.method || 'GET',
        path: signInCall.data.path || '/onemp/redeem/complete_daily_sign/v2/{userId}',
        headers: signInCall.data.header || {},
        authorization: signInCall.data.authorization || signInCall.data.token,
        cloudParams: signInCall.data.containerParams
    };
    
    log("\n🚀 签到API调用信息:", 'green');
    console.log(JSON.stringify(apiCall, null, 2));
    
    return apiCall;
};

// 全局函数 - 生成curl命令
this.generateCurlCommand = function() {
    var apiCall = this.exportSignInAPI();
    if (!apiCall) return null;
    
    var curl = `curl -X ${apiCall.method} \\
  "https://api.nike.com.cn${apiCall.path}" \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "App-Id: wechat:mp:wx096c43d1829a7788" \\
  -H "X-WX-EXCLUDE-CREDENTIALS: cloudbase-access-token" \\
  -H "X-WX-GATEWAY-ID: test-8g5tq0ha6215e83c" \\
  -H "HOST: api.nike.com.cn"`;
    
    log("\n📋 生成的curl命令:", 'cyan');
    console.log(curl);
    
    return curl;
};

// 初始化Hook
function initializeHooks() {
    log("[+] 初始化专业Hook系统...", 'green');
    
    if (ObjC.available) {
        hookWxCloudMethods();
        hookNativeRequests();
        scanMemoryForKeys();
        
        log("\n[✅] 专业Hook系统初始化完成!", 'green');
        log("[💡] 现在请在iPhone上操作Nike小程序:", 'yellow');
        log("  1. 打开Nike小程序", 'white');
        log("  2. 进入福利中心", 'white');
        log("  3. 点击签到按钮", 'white');
        log("  4. 输入 showCapturedData() 查看结果", 'blue');
        log("  5. 输入 exportSignInAPI() 导出API", 'blue');
        log("=====================================\n", 'white');
    } else {
        log("[-] Objective-C运行时不可用", 'red');
    }
}

// 启动Hook系统
initializeHooks();

// 定期状态报告
setInterval(function() {
    if (capturedData.cloudCalls.length > 0 || capturedData.signInCalls.length > 0) {
        log("\n[📈] 实时状态 - 云函数:" + capturedData.cloudCalls.length + 
            " 签到:" + capturedData.signInCalls.length + 
            " 认证:" + capturedData.authCalls.length, 'blue');
    }
}, 30000);

log("🎯 专业Hook脚本已就绪，等待Nike小程序操作...", 'green');
