POST /wxa-qbase/report?token=94_MUT5E7bKmH-80a5g5b43ejKUv1UaG3sBaIFiaG8Ba_h_W6uXHGxcunpEyzZH_1QCo29qVGb-zTQHy0cO9nL_I-awNrmzlC00NE85c-aTMAprooep0m0cOqWJNAPlL8Fl33DAIKCsJNMBoW_2GzKHbmXyUAXPvshFl-N8X3U2u17mwKJXFNngm75zmIwt-mnfA5WrjW02ZHkcxgn7fp4s7GkCk00Y8CUMPAyGvpuBEqvrudeby5KGvuS4UMpd_KwjhyBSJw5-H1pOAQFIRLfI-aPWTzh4YGu3kQbHvd1zfDWfVcDuu3fspQP49tjOCXMGezGCxtb2OGhL_38e&v=1 HTTP/1.1
Host: servicewechat.com
Connection: keep-alive
Content-Length: 387
xweb_xhr: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
Accept: */*
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9

{"report_info_list":[{"log_id":22601,"version":27,"user_log_list":"1754019355893-0.008669194575300576,1754019355893,1754019356298,,,1913,1848,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Windows 10 x64,405,0,,/onemp/redeem/redeem_entrance_info/v2,windows,,1753699712474,,,,,,1754019355893,,,SDK:im,,,,,,,,,100,1256,wx096c43d1829a7788,,microsoft,,wifi,microsoft,pages/swoosh-center/index,0,,,1"}]}

HTTP/1.1 200 OK
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true
Content-Type: application/json; charset=UTF-8
Cache-Control: no-cache, must-revalidate
RetKey: 14
LogicRet: 0
Connection: keep-alive
Content-Length: 23

{"base_resp":{"ret":0}}