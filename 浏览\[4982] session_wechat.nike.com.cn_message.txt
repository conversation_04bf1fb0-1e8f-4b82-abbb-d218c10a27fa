GET /onemp/menu_categories/v1 h2
host: wechat.nike.com.cn
accept: application/json
xweb_xhr: 1
app-id: wechat:mp:wx096c43d1829a7788
authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
content-type: application/json; charset=UTF-8
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1, i



h2 200
server: Tengine
content-type: application/json; charset=utf-8
content-length: 296
set-cookie: acw_tc=74a9a7a317540193481046850e8bc1d867e8c4fb821b75e0d81a883d48;path=/;HttpOnly;Max-Age=3600
set-cookie: cdn_sec_tc=74a9a7a317540193481046850e8bc1d867e8c4fb821b75e0d81a883d48;path=/;HttpOnly;Max-Age=3600
date: Fri, 01 Aug 2025 03:35:48 GMT
etag: W/"128-2oNbE5DlAhz4JEZrshjnKib0RXc"
x-powered-by: Express
x-b3-traceid: 84cb60d88e50bc93
strict-transport-security: max-age=63072000
apigw-requestid: Om1jOgNxZPgEMkA=
via: ens-cache32.l2nm125-7[139,0], ens-cache32.l2nm125-7[139,0], cache15.cn5464[219,0]
access-control-allow-methods: GET, POST, PATCH, PUT, DELETE, OPTIONS
timing-allow-origin: *
eagleid: 74a9a7a317540193481046850e

{"code":0,"msg":"success","data":[{"productTypeId":5193,"productTypeName":"男子","mainTitle":null,"showType":null},{"productTypeId":5542,"productTypeName":"女子","mainTitle":null,"showType":null},{"productTypeId":5818,"productTypeName":"儿童","mainTitle":null,"showType":null}],"search":[]}