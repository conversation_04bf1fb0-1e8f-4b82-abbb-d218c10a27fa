POST /onemp/sub_menu_categories/v1 h2
host: wechat.nike.com.cn
content-length: 11
accept: application/json
xweb_xhr: 1
app-id: wechat:mp:wx096c43d1829a7788
authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
content-type: application/json; charset=UTF-8
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx096c43d1829a7788/100/page-frame.html
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1, i

{"id":5193}

h2 201
server: Tengine
content-type: application/json; charset=utf-8
content-length: 1265
set-cookie: acw_tc=74a9a7a317540193483827510ed54a73aadc57c3ba2ca87729598aeb43;path=/;HttpOnly;Max-Age=3600
set-cookie: cdn_sec_tc=74a9a7a317540193483827510ed54a73aadc57c3ba2ca87729598aeb43;path=/;HttpOnly;Max-Age=3600
date: Fri, 01 Aug 2025 03:35:48 GMT
etag: W/"4f1-KirxWdTVGTdwZccKSChIDrXMo2M"
x-powered-by: Express
x-b3-traceid: 7e99641abad13a13
strict-transport-security: max-age=63072000
apigw-requestid: Om1jRh4j5PgEMdA=
via: ens-cache36.l2nm125-7[189,0], ens-cache36.l2nm125-7[190,0], cache15.cn5464[255,0]
access-control-allow-methods: GET, POST, PATCH, PUT, DELETE, OPTIONS
timing-allow-origin: *
eagleid: 74a9a7a317540193483827510e

{"code":0,"msg":"success","data":[{"productTypeName":"酷夏特惠","productTypeId":6949,"mainTitle":"null","showType":1,"wrap":0},{"productTypeName":"新品","productTypeId":5194,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"鞋类","productTypeId":5236,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"服饰","productTypeId":5265,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"配件","productTypeId":5290,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"跑步","productTypeId":5306,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"篮球","productTypeId":5352,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"足球","productTypeId":5391,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"网球/高尔夫","productTypeId":5421,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"户外/游泳","productTypeId":5442,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"健身训练","productTypeId":5470,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"JORDAN","productTypeId":5490,"mainTitle":null,"showType":1,"wrap":null},{"productTypeName":"进群有礼","productTypeId":5540,"mainTitle":null,"showType":1,"wrap":null}]}