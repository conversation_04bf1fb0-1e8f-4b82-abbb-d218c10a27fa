/**
 * Nike简化API测试
 */

const axios = require('axios');

// 您提供的真实数据
const REAL_USER_ID = "2dfac020-b3da-4cee-ae0b-977eac128709";
const REAL_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgwNWIyZjBiLTgyMjktNGVhZi04MmJhLTA0NDZkOGQ0ODk0YnNpZyJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LKorjqhaR1nwmph1dOro7mg4Qcswv5bY92kLzZBg2ATYqqnUpsuqMjHHVsRK_txTAHzJyzQ8si670U1iDdyh2BzlfqMJnkPy8rukevUL_iSf1cNlHYSIKsYaIbdtHWFPMraEUIdxj4kXw7vNjrhFrwkPOo2mRV-2tIRCVYJPgXnxEcNSbZavlCYjdT3uiQO8garJZrnpUXO9ni-UhVCdN9RzonckmCQq-i6L4oB5Gb9YEHkQafAUlXIG2JTaTQqTOmeIihVKjg8VisXhkW7h2MEy6px-N_5qJ7aII6qSjehP68weP8zO6vKrGmIm0iRtBLCkMKzZiM2Tt8D3WJ_biw";

async function testNikeSignIn() {
    console.log('🎯 Nike签到API测试');
    console.log('=====================================');
    console.log(`👤 用户ID: ${REAL_USER_ID}`);
    console.log(`🔐 Token: ${REAL_TOKEN.substring(0, 50)}...`);
    console.log('=====================================\n');

    try {
        const response = await axios.get(
            `https://wechat.nike.com.cn/onemp/redeem/complete_daily_sign/v2/${REAL_USER_ID}`,
            {
                headers: {
                    "host": "wechat.nike.com.cn",
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Authorization": `Bearer ${REAL_TOKEN}`,
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61"
                },
                timeout: 15000,
                validateStatus: () => true
            }
        );

        console.log(`📊 响应状态: ${response.status}`);
        console.log(`📋 响应头:`, response.headers['content-type']);
        
        // 检查响应类型
        if (typeof response.data === 'string') {
            if (response.data.includes('<html>')) {
                console.log(`📄 响应类型: HTML页面 (长度: ${response.data.length})`);
                console.log(`❌ 这不是API响应，可能是登录页面或错误页面`);
                
                // 检查是否包含错误信息
                if (response.data.includes('error') || response.data.includes('unauthorized')) {
                    console.log(`🔍 可能包含错误信息`);
                }
            } else {
                console.log(`📄 响应类型: 文本 (长度: ${response.data.length})`);
                console.log(`📥 响应内容: ${response.data.substring(0, 200)}...`);
            }
        } else {
            console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
            
            if (response.data.points !== undefined) {
                console.log(`🎉 签到成功! 获得 ${response.data.points} 积分`);
                return true;
            }
        }

        // 检查Token是否过期
        if (response.status === 401 || response.status === 403) {
            console.log(`❌ 认证失败: Token可能已过期`);
        } else if (response.status === 404) {
            console.log(`❌ API端点不存在`);
        } else if (response.status === 200 && typeof response.data === 'string' && response.data.includes('<html>')) {
            console.log(`❌ 返回了网页而不是API响应，可能需要不同的请求方式`);
        }

        return false;

    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        if (error.response) {
            console.log(`📊 错误状态: ${error.response.status}`);
        }
        return false;
    }
}

// 测试Token是否有效
async function testTokenValidity() {
    console.log('\n🔍 测试Token有效性...');
    
    try {
        // 尝试一个简单的API调用
        const response = await axios.get(
            'https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2',
            {
                headers: {
                    "Accept": "application/json",
                    "App-Id": "wechat:mp:wx096c43d1829a7788",
                    "Authorization": `Bearer ${REAL_TOKEN}`
                },
                timeout: 10000,
                validateStatus: () => true
            }
        );

        console.log(`📊 Token测试状态: ${response.status}`);
        
        if (response.status === 401) {
            console.log(`❌ Token已过期或无效`);
        } else if (response.status === 200) {
            if (typeof response.data === 'object') {
                console.log(`✅ Token有效，返回了JSON数据`);
                
                // 查找用户ID
                const responseStr = JSON.stringify(response.data);
                const uuidMatch = responseStr.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g);
                if (uuidMatch) {
                    console.log(`🎯 找到用户ID: ${uuidMatch[0]}`);
                    if (uuidMatch[0] === REAL_USER_ID) {
                        console.log(`✅ 用户ID匹配!`);
                    } else {
                        console.log(`⚠️ 用户ID不匹配，实际: ${uuidMatch[0]}`);
                    }
                }
            } else {
                console.log(`⚠️ Token可能有效，但返回了HTML页面`);
            }
        }
        
    } catch (error) {
        console.log(`❌ Token测试失败: ${error.message}`);
    }
}

async function main() {
    await testTokenValidity();
    await testNikeSignIn();
}

main().catch(console.error);
